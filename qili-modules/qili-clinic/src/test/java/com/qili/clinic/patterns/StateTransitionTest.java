package com.qili.clinic.patterns;

import com.qili.clinic.domain.MedicalRecord;
import com.qili.clinic.domain.bo.MedicalRecordBo;
import com.qili.clinic.enums.MedicalRecordStatus;
import com.qili.clinic.mapper.MedicalRecordMapper;
import com.qili.clinic.patterns.service.StateTransitionService;
import com.qili.clinic.patterns.state.MedicalRecordContext;
import com.qili.clinic.patterns.state.impl.StateFactory;
import com.qili.clinic.service.IMedicalRecordLogService;
import com.qili.clinic.service.IPaymentService;
import com.qili.common.core.exception.ServiceException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Date;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.Mockito.*;

/**
 * 状态转换测试
 *
 * <AUTHOR>
 * @date 2025-08-28
 */
@ExtendWith(MockitoExtension.class)
class StateTransitionTest {

    @Mock
    private MedicalRecordMapper medicalRecordMapper;

    @Mock
    private IMedicalRecordLogService medicalRecordLogService;

    @Mock
    private IPaymentService paymentService;

    private StateTransitionService stateTransitionService;

    private MedicalRecord testRecord;

    @BeforeEach
    void setUp() {
        stateTransitionService = new StateTransitionService(
                medicalRecordMapper, medicalRecordLogService, paymentService);

        testRecord = new MedicalRecord();
        testRecord.setId(1L);
        testRecord.setPatientId(100L);
        testRecord.setPatientName("测试患者");
        testRecord.setVisitNo("20250828001");
        testRecord.setStatus(MedicalRecordStatus.WAITING.getCode());
        testRecord.setVisitTime(new Date());
        testRecord.setUpdateTime(new Date());
    }

    @Test
    void testWaitingToInProgress() {
        // 准备数据
        when(medicalRecordMapper.selectById(1L)).thenReturn(testRecord);
        when(medicalRecordMapper.updateById(any(MedicalRecord.class))).thenReturn(1);

        // 执行转换
        boolean result = stateTransitionService.transitionTo(1L, MedicalRecordStatus.IN_PROGRESS);

        // 验证结果
        assertTrue(result);
        verify(medicalRecordMapper).updateById(any(MedicalRecord.class));
        verify(medicalRecordLogService).insertAsync(any());
    }

    @Test
    void testInProgressToComplete() {
        // 准备数据
        testRecord.setStatus(MedicalRecordStatus.IN_PROGRESS.getCode());
        when(medicalRecordMapper.selectById(1L)).thenReturn(testRecord);
        when(medicalRecordMapper.updateById(any(MedicalRecord.class))).thenReturn(1);

        MedicalRecordBo bo = new MedicalRecordBo();
        bo.setId(1L);

        // 执行完成
        boolean result = stateTransitionService.complete(1L, bo);

        // 验证结果
        assertTrue(result);
        verify(paymentService).savePayment(any(MedicalRecordBo.class));
        verify(medicalRecordMapper).updateById(any(MedicalRecord.class));
        verify(medicalRecordLogService).insertAsync(any());
    }

    @Test
    void testInProgressToCancel() {
        // 准备数据
        testRecord.setStatus(MedicalRecordStatus.IN_PROGRESS.getCode());
        when(medicalRecordMapper.selectById(1L)).thenReturn(testRecord);
        when(medicalRecordMapper.updateById(any(MedicalRecord.class))).thenReturn(1);

        // 执行取消
        boolean result = stateTransitionService.cancel(1L);

        // 验证结果
        assertTrue(result);
        verify(medicalRecordMapper).updateById(any(MedicalRecord.class));
        verify(medicalRecordLogService).insertAsync(any());
    }

    @Test
    void testInvalidTransition() {
        // 准备数据：等待状态不能直接转换到已完成
        when(medicalRecordMapper.selectById(1L)).thenReturn(testRecord);

        // 执行无效转换，应该抛出异常
        assertThrows(ServiceException.class, () -> {
            stateTransitionService.transitionTo(1L, MedicalRecordStatus.COMPLETED);
        });
    }

    @Test
    void testCompletedStateCannotCancel() {
        // 准备数据：已完成状态
        testRecord.setStatus(MedicalRecordStatus.COMPLETED.getCode());
        when(medicalRecordMapper.selectById(1L)).thenReturn(testRecord);

        // 执行取消，应该抛出异常
        assertThrows(ServiceException.class, () -> {
            stateTransitionService.cancel(1L);
        });
    }

    @Test
    void testStateFactory() {
        // 测试状态工厂
        assertEquals(MedicalRecordStatus.WAITING, 
                StateFactory.createState(MedicalRecordStatus.WAITING).getStatus());
        assertEquals(MedicalRecordStatus.IN_PROGRESS, 
                StateFactory.createState(MedicalRecordStatus.IN_PROGRESS).getStatus());
        assertEquals(MedicalRecordStatus.COMPLETED, 
                StateFactory.createState(MedicalRecordStatus.COMPLETED).getStatus());
    }

    @Test
    void testValidTransitions() {
        // 准备数据
        when(medicalRecordMapper.selectById(1L)).thenReturn(testRecord);

        // 测试等待状态的有效转换
        MedicalRecordStatus[] validTransitions = stateTransitionService.getValidTransitions(1L);
        assertEquals(2, validTransitions.length);
        assertTrue(java.util.Arrays.asList(validTransitions).contains(MedicalRecordStatus.IN_PROGRESS));
        assertTrue(java.util.Arrays.asList(validTransitions).contains(MedicalRecordStatus.CANCELLED));
    }

    @Test
    void testCanTransitionTo() {
        // 准备数据
        when(medicalRecordMapper.selectById(1L)).thenReturn(testRecord);

        // 测试是否可以转换
        assertTrue(stateTransitionService.canTransitionTo(1L, MedicalRecordStatus.IN_PROGRESS));
        assertTrue(stateTransitionService.canTransitionTo(1L, MedicalRecordStatus.CANCELLED));
        assertFalse(stateTransitionService.canTransitionTo(1L, MedicalRecordStatus.COMPLETED));
    }

    @Test
    void testFullWorkflow() {
        // 测试完整工作流程：等待 -> 就诊中 -> 已开方 -> 取药中 -> 煎药中 -> 已完成
        when(medicalRecordMapper.selectById(1L)).thenReturn(testRecord);
        when(medicalRecordMapper.updateById(any(MedicalRecord.class))).thenReturn(1);

        // 1. 开始就诊
        assertTrue(stateTransitionService.startConsultation(1L));
        testRecord.setStatus(MedicalRecordStatus.IN_PROGRESS.getCode());

        // 2. 完成开方
        MedicalRecordBo bo = new MedicalRecordBo();
        bo.setId(1L);
        assertTrue(stateTransitionService.complete(1L, bo));
        testRecord.setStatus(MedicalRecordStatus.PRESCRIPTION_DONE.getCode());

        // 3. 开始取药
        assertTrue(stateTransitionService.startDispensing(1L));
        testRecord.setStatus(MedicalRecordStatus.DISPENSING.getCode());

        // 4. 开始煎药
        assertTrue(stateTransitionService.startDecocting(1L));
        testRecord.setStatus(MedicalRecordStatus.DECOCTING.getCode());

        // 5. 完成流程
        assertTrue(stateTransitionService.finishProcess(1L));

        // 验证调用次数
        verify(medicalRecordMapper, times(5)).updateById(any(MedicalRecord.class));
        verify(medicalRecordLogService, times(5)).insertAsync(any());
    }

    @Test
    void testRecordNotFound() {
        // 准备数据：记录不存在
        when(medicalRecordMapper.selectById(1L)).thenReturn(null);

        // 执行操作，应该抛出异常
        assertThrows(ServiceException.class, () -> {
            stateTransitionService.getCurrentStatus(1L);
        });
    }
}
