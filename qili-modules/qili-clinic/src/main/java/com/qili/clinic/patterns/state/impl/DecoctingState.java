package com.qili.clinic.patterns.state.impl;

import com.qili.clinic.enums.MedicalRecordStatus;
import com.qili.clinic.patterns.state.AbstractMedicalRecordState;
import com.qili.clinic.patterns.state.MedicalRecordState;
import lombok.extern.slf4j.Slf4j;

/**
 * 煎药中状态
 *
 * <AUTHOR>
 * @date 2025-08-28
 */
@Slf4j
public class DecoctingState extends AbstractMedicalRecordState {

    @Override
    public MedicalRecordStatus getStatus() {
        return MedicalRecordStatus.DECOCTING;
    }

    @Override
    public MedicalRecordStatus[] getValidTransitions() {
        return new MedicalRecordStatus[]{
            MedicalRecordStatus.COMPLETED  // 完成煎药
        };
    }

    @Override
    protected MedicalRecordState createStateInstance(MedicalRecordStatus status) {
        return StateFactory.createState(status);
    }
}
