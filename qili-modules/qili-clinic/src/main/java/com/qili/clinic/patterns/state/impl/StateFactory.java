package com.qili.clinic.patterns.state.impl;

import com.qili.clinic.enums.MedicalRecordStatus;
import com.qili.clinic.patterns.state.MedicalRecordState;
import com.qili.common.core.exception.ServiceException;

/**
 * 状态工厂类
 * 负责创建具体的状态实例
 *
 * <AUTHOR>
 * @date 2025-08-28
 */
public class StateFactory {

    /**
     * 创建状态实例
     *
     * @param status 状态枚举
     * @return 状态实例
     */
    public static MedicalRecordState createState(MedicalRecordStatus status) {
        if (status == null) {
            throw new ServiceException("状态不能为空");
        }

        switch (status) {
            case WAITING:
                return new WaitingState();
            case IN_PROGRESS:
                return new InProgressState();
            case PRESCRIPTION_DONE:
                return new PrescriptionDoneState();
            case DISPENSING:
                return new DispensingState();
            case DECOCTING:
                return new DecoctingState();
            case COMPLETED:
                return new CompletedState();
            case CANCELLED:
                return new CancelledState();
            default:
                throw new ServiceException("未知的状态: " + status.getCode());
        }
    }

    /**
     * 根据状态代码创建状态实例
     *
     * @param statusCode 状态代码
     * @return 状态实例
     */
    public static MedicalRecordState createState(String statusCode) {
        MedicalRecordStatus status = MedicalRecordStatus.fromCode(statusCode);
        return createState(status);
    }
}
