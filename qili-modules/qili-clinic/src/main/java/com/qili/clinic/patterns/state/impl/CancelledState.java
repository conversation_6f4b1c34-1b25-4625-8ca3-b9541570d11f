package com.qili.clinic.patterns.state.impl;

import com.qili.clinic.domain.bo.MedicalRecordBo;
import com.qili.clinic.enums.MedicalRecordStatus;
import com.qili.clinic.patterns.state.AbstractMedicalRecordState;
import com.qili.clinic.patterns.state.MedicalRecordContext;
import com.qili.clinic.patterns.state.MedicalRecordState;
import com.qili.common.core.exception.ServiceException;
import lombok.extern.slf4j.Slf4j;

/**
 * 已取消状态
 *
 * <AUTHOR>
 * @date 2025-08-28
 */
@Slf4j
public class CancelledState extends AbstractMedicalRecordState {

    @Override
    public MedicalRecordStatus getStatus() {
        return MedicalRecordStatus.CANCELLED;
    }

    @Override
    public MedicalRecordStatus[] getValidTransitions() {
        // 已取消状态不能转换到其他状态
        return new MedicalRecordStatus[]{};
    }

    @Override
    public boolean cancel(MedicalRecordContext context) {
        throw new ServiceException("就诊记录已经取消");
    }

    @Override
    public boolean complete(MedicalRecordContext context, MedicalRecordBo bo) {
        throw new ServiceException("已取消的就诊记录不能完成");
    }

    @Override
    protected MedicalRecordState createStateInstance(MedicalRecordStatus status) {
        return StateFactory.createState(status);
    }
}
