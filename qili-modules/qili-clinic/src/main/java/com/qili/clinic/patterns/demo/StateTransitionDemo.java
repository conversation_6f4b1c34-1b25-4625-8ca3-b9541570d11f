package com.qili.clinic.patterns.demo;

import com.qili.clinic.enums.MedicalRecordStatus;
import com.qili.clinic.patterns.state.impl.StateFactory;
import com.qili.clinic.patterns.state.MedicalRecordState;
import lombok.extern.slf4j.Slf4j;

/**
 * 状态转换演示类
 * 用于演示状态模式的使用
 *
 * <AUTHOR>
 * @date 2025-08-28
 */
@Slf4j
public class StateTransitionDemo {

    /**
     * 演示状态转换规则
     */
    public static void demonstrateStateTransitions() {
        log.info("=== 医疗记录状态转换规则演示 ===");

        // 演示每个状态的可转换状态
        for (MedicalRecordStatus status : MedicalRecordStatus.values()) {
            MedicalRecordState state = StateFactory.createState(status);
            MedicalRecordStatus[] validTransitions = state.getValidTransitions();
            
            log.info("状态: {} ({})", status.getLabel(), status.getCode());
            if (validTransitions.length == 0) {
                log.info("  -> 无可转换状态（终态）");
            } else {
                for (MedicalRecordStatus target : validTransitions) {
                    log.info("  -> 可转换到: {} ({})", target.getLabel(), target.getCode());
                }
            }
            log.info("");
        }
    }

    /**
     * 演示完整的工作流程
     */
    public static void demonstrateWorkflow() {
        log.info("=== 完整工作流程演示 ===");
        
        // 定义完整的工作流程路径
        MedicalRecordStatus[] workflowPath = {
            MedicalRecordStatus.WAITING,
            MedicalRecordStatus.IN_PROGRESS,
            MedicalRecordStatus.PRESCRIPTION_DONE,
            MedicalRecordStatus.DISPENSING,
            MedicalRecordStatus.DECOCTING,
            MedicalRecordStatus.COMPLETED
        };

        log.info("标准工作流程:");
        for (int i = 0; i < workflowPath.length; i++) {
            MedicalRecordStatus status = workflowPath[i];
            if (i == 0) {
                log.info("{}. {} ({})", i + 1, status.getLabel(), status.getCode());
            } else {
                log.info("{}. {} ({}) <- 从上一状态转换", i + 1, status.getLabel(), status.getCode());
            }
        }

        log.info("");
        log.info("可选的分支路径:");
        log.info("- 从 就诊中 可以转换到 已取消");
        log.info("- 从 已开方 可以直接转换到 已完成（跳过取药和煎药）");
        log.info("- 从 取药中 可以直接转换到 已完成（跳过煎药）");
    }

    /**
     * 演示状态验证
     */
    public static void demonstrateStateValidation() {
        log.info("=== 状态验证演示 ===");

        // 测试有效转换
        MedicalRecordState waitingState = StateFactory.createState(MedicalRecordStatus.WAITING);
        log.info("等待状态 -> 就诊中: {}", 
                waitingState.canTransitionTo(MedicalRecordStatus.IN_PROGRESS) ? "✓ 允许" : "✗ 禁止");
        log.info("等待状态 -> 已完成: {}", 
                waitingState.canTransitionTo(MedicalRecordStatus.COMPLETED) ? "✓ 允许" : "✗ 禁止");

        // 测试终态
        MedicalRecordState completedState = StateFactory.createState(MedicalRecordStatus.COMPLETED);
        log.info("已完成状态 -> 就诊中: {}", 
                completedState.canTransitionTo(MedicalRecordStatus.IN_PROGRESS) ? "✓ 允许" : "✗ 禁止");
        log.info("已完成状态的可转换状态数量: {}", completedState.getValidTransitions().length);

        MedicalRecordState cancelledState = StateFactory.createState(MedicalRecordStatus.CANCELLED);
        log.info("已取消状态的可转换状态数量: {}", cancelledState.getValidTransitions().length);
    }

    public static void main(String[] args) {
        demonstrateStateTransitions();
        demonstrateWorkflow();
        demonstrateStateValidation();
    }
}
