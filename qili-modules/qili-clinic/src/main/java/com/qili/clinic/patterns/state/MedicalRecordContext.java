package com.qili.clinic.patterns.state;

import com.qili.clinic.domain.MedicalRecord;
import com.qili.clinic.domain.bo.MedicalRecordBo;
import com.qili.clinic.enums.MedicalRecordStatus;
import com.qili.clinic.mapper.MedicalRecordMapper;
import com.qili.clinic.service.IMedicalRecordLogService;
import com.qili.clinic.service.IPaymentService;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

/**
 * 医疗记录状态上下文
 * 维护当前状态并提供状态转换的环境
 *
 * <AUTHOR>
 * @date 2025-08-28
 */
@Slf4j
@Data
public class MedicalRecordContext {

    /**
     * 当前医疗记录
     */
    private MedicalRecord medicalRecord;

    /**
     * 当前状态
     */
    private MedicalRecordState currentState;

    /**
     * 医疗记录数据访问层
     */
    private MedicalRecordMapper medicalRecordMapper;

    /**
     * 医疗记录日志服务
     */
    private IMedicalRecordLogService medicalRecordLogService;

    /**
     * 支付服务
     */
    private IPaymentService paymentService;

    /**
     * 构造函数
     *
     * @param medicalRecord           医疗记录
     * @param currentState            当前状态
     * @param medicalRecordMapper     数据访问层
     * @param medicalRecordLogService 日志服务
     * @param paymentService          支付服务
     */
    public MedicalRecordContext(MedicalRecord medicalRecord,
                                MedicalRecordState currentState,
                                MedicalRecordMapper medicalRecordMapper,
                                IMedicalRecordLogService medicalRecordLogService,
                                IPaymentService paymentService) {
        this.medicalRecord = medicalRecord;
        this.currentState = currentState;
        this.medicalRecordMapper = medicalRecordMapper;
        this.medicalRecordLogService = medicalRecordLogService;
        this.paymentService = paymentService;
    }

    /**
     * 转换状态
     *
     * @param targetStatus 目标状态
     * @return 是否转换成功
     */
    public boolean transitionTo(MedicalRecordStatus targetStatus) {
        if (currentState == null) {
            log.error("当前状态为空，无法转换");
            return false;
        }

        return currentState.transitionTo(this, targetStatus);
    }

    /**
     * 取消就诊
     *
     * @return 是否取消成功
     */
    public boolean cancel() {
        if (currentState == null) {
            log.error("当前状态为空，无法取消");
            return false;
        }

        return currentState.cancel(this);
    }

    /**
     * 完成就诊
     *
     * @param bo 医疗记录业务对象
     * @return 是否完成成功
     */
    public boolean complete(MedicalRecordBo bo) {
        if (currentState == null) {
            log.error("当前状态为空，无法完成");
            return false;
        }

        return currentState.complete(this, bo);
    }

    /**
     * 设置新状态
     *
     * @param newState 新状态
     */
    public void setState(MedicalRecordState newState) {
        log.info("状态转换: {} -> {}",
                 currentState != null ? currentState.getStatus().getLabel() : "null",
                 newState != null ? newState.getStatus().getLabel() : "null");
        this.currentState = newState;
    }

    /**
     * 获取当前状态枚举
     *
     * @return 状态枚举
     */
    public MedicalRecordStatus getCurrentStatus() {
        return currentState != null ? currentState.getStatus() : null;
    }

    /**
     * 验证是否可以转换到目标状态
     *
     * @param targetStatus 目标状态
     * @return 是否可以转换
     */
    public boolean canTransitionTo(MedicalRecordStatus targetStatus) {
        return currentState != null && currentState.canTransitionTo(targetStatus);
    }
}
