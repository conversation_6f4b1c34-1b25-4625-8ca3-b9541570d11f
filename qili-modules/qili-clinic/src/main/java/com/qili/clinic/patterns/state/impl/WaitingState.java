package com.qili.clinic.patterns.state.impl;

import com.qili.clinic.enums.MedicalRecordStatus;
import com.qili.clinic.patterns.state.AbstractMedicalRecordState;
import com.qili.clinic.patterns.state.MedicalRecordState;
import lombok.extern.slf4j.Slf4j;

/**
 * 等待接诊状态
 *
 * <AUTHOR>
 * @date 2025-08-28
 */
@Slf4j
public class WaitingState extends AbstractMedicalRecordState {

    @Override
    public MedicalRecordStatus getStatus() {
        return MedicalRecordStatus.WAITING;
    }

    @Override
    public MedicalRecordStatus[] getValidTransitions() {
        return new MedicalRecordStatus[]{
            MedicalRecordStatus.IN_PROGRESS,  // 开始就诊
            MedicalRecordStatus.CANCELLED     // 取消就诊
        };
    }

    @Override
    protected MedicalRecordState createStateInstance(MedicalRecordStatus status) {
        return StateFactory.createState(status);
    }
}
