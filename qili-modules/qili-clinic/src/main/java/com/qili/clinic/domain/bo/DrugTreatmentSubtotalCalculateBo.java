package com.qili.clinic.domain.bo;

import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 药品小计计算请求对象
 *
 * <AUTHOR>
 * @date 2025-08-27
 */
@Data
public class DrugTreatmentSubtotalCalculateBo {

    /**
     * 药品ID
     */
    @NotNull(message = "药品ID不能为空")
    private Long id;

    /**
     * 数量
     */
    @NotNull(message = "数量不能为空")
    @Positive(message = "数量必须大于0")
    private BigDecimal amount;
}
