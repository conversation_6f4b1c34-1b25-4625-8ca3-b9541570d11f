package com.qili.clinic.service.impl;

import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.qili.clinic.domain.Patient;
import com.qili.clinic.domain.Payment;
import com.qili.clinic.domain.PaymentDetail;
import com.qili.clinic.domain.bo.*;
import com.qili.clinic.domain.vo.DrugVo;
import com.qili.clinic.domain.vo.PaymentVo;
import com.qili.clinic.domain.vo.TreatmentProjectVo;
import com.qili.clinic.enums.PaymentMethod;
import com.qili.clinic.enums.PaymentOrderType;
import com.qili.clinic.enums.PaymentStatus;
import com.qili.clinic.mapper.PatientMapper;
import com.qili.clinic.mapper.PaymentDetailMapper;
import com.qili.clinic.mapper.PaymentMapper;
import com.qili.clinic.service.IDrugService;
import com.qili.clinic.service.IPaymentService;
import com.qili.clinic.service.ITreatmentProjectService;
import com.qili.common.core.exception.ServiceException;
import com.qili.common.core.utils.MapstructUtils;
import com.qili.common.core.utils.StringUtils;
import com.qili.common.mybatis.core.page.PageQuery;
import com.qili.common.mybatis.core.page.TableDataInfo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 支付主Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-08-15
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class PaymentServiceImpl implements IPaymentService {

    private final PaymentMapper baseMapper;
    private final PaymentDetailMapper paymentDetailMapper;
    private final IDrugService drugService;
    private final ITreatmentProjectService treatmentProjectService;
    private final PatientMapper patientMapper;

    /**
     * 查询支付主
     *
     * @param id 主键
     * @return 支付主
     */
    @Override
    public PaymentVo queryById(Long id) {
        return baseMapper.selectVoById(id);
    }

    /**
     * 分页查询支付主列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 支付主分页列表
     */
    @Override
    public TableDataInfo<PaymentVo> queryPageList(PaymentBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<Payment> lqw = buildQueryWrapper(bo);
        Page<PaymentVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的支付主列表
     *
     * @param bo 查询条件
     * @return 支付主列表
     */
    @Override
    public List<PaymentVo> queryList(PaymentBo bo) {
        LambdaQueryWrapper<Payment> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<Payment> buildQueryWrapper(PaymentBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<Payment> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(Payment::getId);
        lqw.eq(StringUtils.isNotBlank(bo.getPaymentNo()), Payment::getPaymentNo, bo.getPaymentNo());
        lqw.eq(bo.getPatientId() != null, Payment::getPatientId, bo.getPatientId());
        lqw.eq(StringUtils.isNotBlank(bo.getOrderType()), Payment::getOrderType, bo.getOrderType());
        lqw.eq(StringUtils.isNotBlank(bo.getPaymentMethod()), Payment::getPaymentMethod, bo.getPaymentMethod());
        lqw.like(bo.getPaymentTime() != null, Payment::getPaymentTime, bo.getPaymentTime());
        return lqw;
    }

    /**
     * 新增支付主
     *
     * @param bo 支付主
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(PaymentBo bo) {
        Payment add = MapstructUtils.convert(bo, Payment.class);
        if (ObjectUtil.isNotNull(add)) {
            throw new ServiceException("转换失败");
        }
        Long paidAmountLong = NumberUtil.mul(bo.getPaidAmount(), 100).longValue();
        add.setPaymentMethod(PaymentMethod.getByCode(bo.getPaymentMethod()).getCode());
        add.setPaidAmount(paidAmountLong);
        Long totalAmountLong = NumberUtil.mul(bo.getTotalAmount(), 100).longValue();
        add.setTotalAmount(totalAmountLong);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(Payment entity) {
        // TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除支付主信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            // TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }

    @Override
    @Transactional(rollbackFor = ServiceException.class)
    public void savePayment(MedicalRecordBo medicalRecordBo) {
        PaymentBo paymentBo = medicalRecordBo.getPayment();
        if (ObjectUtil.isNull(paymentBo)) {
            return;
        }
        Long id = medicalRecordBo.getId();

        Long patientId = medicalRecordBo.getPatientId();
        if (ObjectUtil.isNull(patientId)) {
            return;
        }
        Patient patient = patientMapper.selectById(patientId);
        if (patient == null) {
            log.warn("患者不存在，ID: {}", patientId);
            return;
        }
        Payment payment = MapstructUtils.convert(paymentBo, Payment.class);
        // 计算支付详情和总金额
        List<PaymentDetail> paymentDetails = calculatePaymentDetailsFromBo(medicalRecordBo);
        Long totalAmount = paymentDetails.stream()
            .map(detail -> BigDecimal.valueOf(detail.getAmount()))
            .reduce(BigDecimal.ZERO, BigDecimal::add)
            .longValue();
        payment.setPatientId(patient.getId());
        payment.setPatientName(patient.getName());
        payment.setSourceId(id);
        payment.setOrderType(PaymentOrderType.PRESCRIPTION.getCode());
        payment.setStatus(PaymentStatus.PAID.getCode());
        payment.setPaymentMethod(PaymentMethod.getByCode(payment.getPaymentMethod()).getCode());
        payment.setPaymentTime(new Date());
        payment.setPaymentNo("ZF" + IdUtil.getSnowflakeNextIdStr());
        payment.setTotalAmount(totalAmount);
        payment.setPaidAmount(payment.getPaidAmount());

        baseMapper.insert(payment);
        this.savePaymentDetail(payment.getId(), paymentDetails);
    }

    /**
     * 保存支付项
     *
     * @param paymentId         支付ID
     * @param paymentDetailList 支付项列表
     */
    private void savePaymentDetail(Long paymentId, List<PaymentDetail> paymentDetailList) {
        if (ObjectUtil.isEmpty(paymentDetailList)) {
            return;
        }
        paymentDetailList.forEach(paymentDetail -> paymentDetail.setPaymentId(paymentId));
        paymentDetailMapper.insertBatch(paymentDetailList);
    }


    /**
     * 根据病历记录BO中的处方数据计算支付详情
     *
     * @param medicalRecordBo 病历记录业务对象
     * @return 支付详情列表
     */
    private List<PaymentDetail> calculatePaymentDetailsFromBo(MedicalRecordBo medicalRecordBo) {
        // 收集所有需要查询的药品ID和诊疗项目ID
        Set<Long> drugIds = new HashSet<>();
        Set<Long> projectIds = new HashSet<>();

        collectIds(medicalRecordBo, drugIds, projectIds);

        // 批量查询药品和诊疗项目信息
        Map<Long, DrugVo> drugMap = drugIds.isEmpty() ?
            Collections.emptyMap() :
            drugService.queryByIds(drugIds).stream()
                .collect(Collectors.toMap(DrugVo::getId, drug -> drug));

        Map<Long, TreatmentProjectVo> projectMap = projectIds.isEmpty() ?
            Collections.emptyMap() :
            treatmentProjectService.queryByIds(projectIds).stream()
                .collect(Collectors.toMap(TreatmentProjectVo::getId, project -> project));

        // 生成支付详情
        List<PaymentDetail> paymentDetails = new ArrayList<>();

        // 处理中药饮片处方
        if (medicalRecordBo.getTcmPrescriptionData() != null) {
            paymentDetails.addAll(processTcmPrescriptionFromBo(medicalRecordBo.getTcmPrescriptionData(), drugMap));
        }
        // 处理中成药处方
        if (medicalRecordBo.getTcmpPrescriptionData() != null) {
            paymentDetails.addAll(processMedicinePrescriptionFromBo(medicalRecordBo.getTcmpPrescriptionData(), drugMap));
        }
        // 处理西药处方
        if (medicalRecordBo.getWmPrescriptionData() != null) {
            paymentDetails.addAll(processMedicinePrescriptionFromBo(medicalRecordBo.getWmPrescriptionData(), drugMap));
        }
        // 处理诊疗项目
        if (medicalRecordBo.getTreatmentProjectData() != null) {
            paymentDetails.addAll(processTreatmentProjectFromBo(medicalRecordBo.getTreatmentProjectData(), projectMap));
        }
        return paymentDetails;
    }

    /**
     * 收集所有需要查询的药品ID和诊疗项目ID
     *
     * @param medicalRecordBo 病历记录业务对象
     * @param drugIds         药品ID集合
     * @param projectIds      诊疗项目ID集合
     */
    private void collectIds(MedicalRecordBo medicalRecordBo, Set<Long> drugIds, Set<Long> projectIds) {
        // 收集中药饮片处方中的药品ID
        if (medicalRecordBo.getTcmPrescriptionData() != null &&
            medicalRecordBo.getTcmPrescriptionData().getTableData() != null) {
            medicalRecordBo.getTcmPrescriptionData().getTableData().stream()
                .filter(item -> StrUtil.isNotBlank(item.getId()) && item.getAmount() != null)
                .forEach(item -> drugIds.add(Long.parseLong(item.getId())));
        }

        // 收集中成药处方中的药品ID
        if (medicalRecordBo.getTcmpPrescriptionData() != null &&
            medicalRecordBo.getTcmpPrescriptionData().getTableData() != null) {
            medicalRecordBo.getTcmpPrescriptionData().getTableData().stream()
                .filter(item -> StrUtil.isNotBlank(item.getId()) && item.getTotalAmount() != null)
                .forEach(item -> drugIds.add(Long.parseLong(item.getId())));
        }

        // 收集西药处方中的药品ID
        if (medicalRecordBo.getWmPrescriptionData() != null &&
            medicalRecordBo.getWmPrescriptionData().getTableData() != null) {
            medicalRecordBo.getWmPrescriptionData().getTableData().stream()
                .filter(item -> StrUtil.isNotBlank(item.getId()) && item.getTotalAmount() != null)
                .forEach(item -> drugIds.add(Long.parseLong(item.getId())));
        }

        // 收集诊疗项目ID
        if (medicalRecordBo.getTreatmentProjectData() != null &&
            medicalRecordBo.getTreatmentProjectData().getTableData() != null) {
            medicalRecordBo.getTreatmentProjectData().getTableData().stream()
                .filter(item -> StrUtil.isNotBlank(item.getId()) && item.getAmount() != null)
                .forEach(item -> projectIds.add(Long.parseLong(item.getId())));
        }
    }

    /**
     * 处理中药饮片处方BO数据
     *
     * @param prescriptionData 中药饮片处方数据
     * @param drugMap          药品信息Map
     * @return 支付详情列表
     */
    private List<PaymentDetail> processTcmPrescriptionFromBo(TcmPrescriptionDataBo prescriptionData, Map<Long, DrugVo> drugMap) {
        if (prescriptionData == null || prescriptionData.getTableData() == null) {
            return new ArrayList<>();
        }
        return prescriptionData.getTableData().stream()
            .filter(item -> StrUtil.isNotBlank(item.getId()) && item.getAmount() != null)
            .map(item -> createPaymentDetailFromDrug(item.getId(), item.getAmount(), drugMap))
            .filter(Objects::nonNull)
            .toList();
    }

    /**
     * 处理中成药/西药处方BO数据
     *
     * @param prescriptionData 中成药/西药处方数据
     * @param drugMap          药品信息Map
     * @return 支付详情列表
     */
    private List<PaymentDetail> processMedicinePrescriptionFromBo(MedicinePrescriptionDataBo prescriptionData, Map<Long, DrugVo> drugMap) {
        if (prescriptionData == null || prescriptionData.getTableData() == null) {
            return new ArrayList<>();
        }

        return prescriptionData.getTableData().stream()
            .filter(item -> StrUtil.isNotBlank(item.getId()) && item.getTotalAmount() != null)
            .map(item -> createPaymentDetailFromDrug(item.getId(), item.getTotalAmount(), drugMap))
            .filter(Objects::nonNull)
            .toList();
    }

    /**
     * 处理诊疗项目BO数据
     *
     * @param projectData 诊疗项目数据
     * @param projectMap  诊疗项目信息Map
     * @return 支付详情列表
     */
    private List<PaymentDetail> processTreatmentProjectFromBo(TreatmentProjectDataBo projectData, Map<Long, TreatmentProjectVo> projectMap) {
        if (projectData == null || projectData.getTableData() == null) {
            return new ArrayList<>();
        }
        return projectData.getTableData().stream()
            .filter(item -> StrUtil.isNotBlank(item.getId()) && item.getAmount() != null)
            .map(item -> createPaymentDetailFromTreatmentProject(item.getId(), item.getAmount(), projectMap))
            .filter(Objects::nonNull)
            .collect(Collectors.toList());
    }

    /**
     * 根据药品ID和数量创建支付详情（从Map中获取药品信息）
     *
     * @param drugId   药品ID
     * @param quantity 数量
     * @param drugMap  药品信息Map
     * @return 支付详情
     */
    private PaymentDetail createPaymentDetailFromDrug(String drugId, BigDecimal quantity, Map<Long, DrugVo> drugMap) {
        try {
            Long id = Long.parseLong(drugId);
            DrugVo drugVo = drugMap.get(id);
            if (drugVo == null) {
                log.warn("药品不存在，ID: {}", drugId);
                return null;
            }
            PaymentDetail detail = new PaymentDetail();
            detail.setDrugId(id);
            detail.setItemName(drugVo.getName());
            detail.setItemCode(drugVo.getCode());
            detail.setUnit(drugVo.getUnit());
            detail.setQuantity(quantity.longValue());
            detail.setPrice(drugVo.getPrice()); // 数据库中已经是分为单位
            // 修复计算错误：使用 BigDecimal 进行精确计算
            detail.setAmount(NumberUtil.mul(BigDecimal.valueOf(drugVo.getPrice()), quantity).longValue());
            return detail;
        } catch (NumberFormatException e) {
            log.warn("药品ID格式错误: {}", drugId);
            return null;
        } catch (Exception e) {
            log.error("创建药品支付详情失败，drugId: {}, quantity: {}", drugId, quantity, e);
            return null;
        }
    }

    /**
     * 根据诊疗项目ID和数量创建支付详情（从Map中获取项目信息）
     *
     * @param projectId  诊疗项目ID
     * @param quantity   数量
     * @param projectMap 诊疗项目信息Map
     * @return 支付详情
     */
    private PaymentDetail createPaymentDetailFromTreatmentProject(String projectId, BigDecimal quantity, Map<Long, TreatmentProjectVo> projectMap) {
        try {
            Long id = Long.parseLong(projectId);
            TreatmentProjectVo projectVo = projectMap.get(id);
            if (projectVo == null) {
                log.warn("诊疗项目不存在，ID: {}", projectId);
                return null;
            }
            PaymentDetail detail = new PaymentDetail();
            detail.setItemName(projectVo.getName());
            detail.setItemCode(projectVo.getCode());
            detail.setUnit(projectVo.getUnit());
            detail.setQuantity(quantity.longValue());
            detail.setPrice(projectVo.getPrice()); // 数据库中已经是分为单位
            detail.setAmount(NumberUtil.mul(BigDecimal.valueOf(projectVo.getPrice()), quantity).longValue());
            return detail;
        } catch (NumberFormatException e) {
            log.warn("诊疗项目ID格式错误: {}", projectId);
            return null;
        } catch (Exception e) {
            log.error("创建诊疗项目支付详情失败，projectId: {}, quantity: {}", projectId, quantity, e);
            return null;
        }
    }


}
