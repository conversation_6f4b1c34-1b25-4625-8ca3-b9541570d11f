package com.qili.clinic.service;

import com.qili.clinic.domain.bo.MedicalRecordBo;
import com.qili.clinic.domain.vo.MedicalRecordVo;
import com.qili.common.mybatis.core.page.PageQuery;
import com.qili.common.mybatis.core.page.TableDataInfo;

import java.util.Collection;
import java.util.List;

/**
 * 病历/就诊记录Service接口
 *
 * <AUTHOR>
 * @date 2025-08-06
 */
public interface IMedicalRecordService {

    /**
     * 查询病历/就诊记录
     *
     * @param id 主键
     * @return 病历/就诊记录
     */
    MedicalRecordVo queryById(Long id);

    /**
     * 分页查询病历/就诊记录列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 病历/就诊记录分页列表
     */
    TableDataInfo<MedicalRecordVo> queryPageList(MedicalRecordBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的病历/就诊记录列表
     *
     * @param bo 查询条件
     * @return 病历/就诊记录列表
     */
    List<MedicalRecordVo> queryList(MedicalRecordBo bo);

    /**
     * 新增病历/就诊记录
     *
     * @param bo 病历/就诊记录
     * @return 是否新增成功
     */
    Long insertByBo(MedicalRecordBo bo);

    /**
     * 修改病历/就诊记录
     *
     * @param bo 病历/就诊记录
     * @return 是否修改成功
     */
    Boolean updateByBo(MedicalRecordBo bo);

    /**
     * 校验并批量删除病历/就诊记录信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 根据患者id查询最新就诊记录
     *
     * @param patientId 患者id
     * @return 就诊记录
     */
    MedicalRecordVo queryLatestByPatientId(Long patientId);

    /**
     * 取消就诊
     *
     * @param id 主键
     */
    void cancelById(Long id);

    /**
     * 完成就诊
     *
     * @param id 主键
     */
    void complete(MedicalRecordBo bo);

    /**
     * 保存处方,用于定时保存处方
     *
     * @param bo 病历/就诊记录
     */
    void savePrescription(MedicalRecordBo bo);

    /**
     * 通用状态转换方法
     *
     * @param recordId 医疗记录ID
     * @param targetStatus 目标状态
     * @param data 额外数据
     * @param action 操作类型
     * @return 是否成功
     */
    boolean transitionState(Long recordId, com.qili.clinic.enums.MedicalRecordStatus targetStatus, Object data, String action);

    /**
     * 获取可转换的状态列表
     *
     * @param recordId 医疗记录ID
     * @return 可转换的状态数组
     */
    com.qili.clinic.enums.MedicalRecordStatus[] getValidTransitions(Long recordId);

    /**
     * 检查是否可以转换到目标状态
     *
     * @param recordId 医疗记录ID
     * @param targetStatus 目标状态
     * @return 是否可以转换
     */
    boolean canTransitionTo(Long recordId, com.qili.clinic.enums.MedicalRecordStatus targetStatus);
}
