package com.qili.clinic.patterns.service;

import com.qili.clinic.domain.MedicalRecord;
import com.qili.clinic.domain.bo.MedicalRecordBo;
import com.qili.clinic.enums.MedicalRecordStatus;
import com.qili.clinic.mapper.MedicalRecordMapper;
import com.qili.clinic.patterns.state.MedicalRecordContext;
import com.qili.clinic.patterns.state.MedicalRecordState;
import com.qili.clinic.patterns.state.impl.StateFactory;
import com.qili.clinic.service.IMedicalRecordLogService;
import com.qili.clinic.service.IPaymentService;
import com.qili.common.core.exception.ServiceException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 状态转换服务
 * 提供统一的状态转换入口
 *
 * <AUTHOR>
 * @date 2025-08-28
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class StateTransitionService {

    private final MedicalRecordMapper medicalRecordMapper;
    private final IMedicalRecordLogService medicalRecordLogService;
    private final IPaymentService paymentService;

    /**
     * 创建医疗记录上下文
     *
     * @param recordId 医疗记录ID
     * @return 上下文对象
     */
    public MedicalRecordContext createContext(Long recordId) {
        MedicalRecord record = medicalRecordMapper.selectById(recordId);
        if (record == null) {
            throw new ServiceException("就诊记录不存在");
        }

        MedicalRecordState currentState = StateFactory.createState(record.getStatus());
        return new MedicalRecordContext(record, currentState, medicalRecordMapper, 
                                      medicalRecordLogService, paymentService);
    }

    /**
     * 转换状态
     *
     * @param recordId 医疗记录ID
     * @param targetStatus 目标状态
     * @return 是否转换成功
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean transitionTo(Long recordId, MedicalRecordStatus targetStatus) {
        MedicalRecordContext context = createContext(recordId);
        return context.transitionTo(targetStatus);
    }

    /**
     * 取消就诊
     *
     * @param recordId 医疗记录ID
     * @return 是否取消成功
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean cancel(Long recordId) {
        MedicalRecordContext context = createContext(recordId);
        return context.cancel();
    }

    /**
     * 完成就诊
     *
     * @param recordId 医疗记录ID
     * @param bo 医疗记录业务对象
     * @return 是否完成成功
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean complete(Long recordId, MedicalRecordBo bo) {
        MedicalRecordContext context = createContext(recordId);
        return context.complete(bo);
    }

    /**
     * 检查是否可以转换到目标状态
     *
     * @param recordId 医疗记录ID
     * @param targetStatus 目标状态
     * @return 是否可以转换
     */
    public boolean canTransitionTo(Long recordId, MedicalRecordStatus targetStatus) {
        MedicalRecordContext context = createContext(recordId);
        return context.canTransitionTo(targetStatus);
    }

    /**
     * 获取当前状态
     *
     * @param recordId 医疗记录ID
     * @return 当前状态
     */
    public MedicalRecordStatus getCurrentStatus(Long recordId) {
        MedicalRecordContext context = createContext(recordId);
        return context.getCurrentStatus();
    }

    /**
     * 获取可转换的状态列表
     *
     * @param recordId 医疗记录ID
     * @return 可转换的状态数组
     */
    public MedicalRecordStatus[] getValidTransitions(Long recordId) {
        MedicalRecordContext context = createContext(recordId);
        return context.getCurrentState().getValidTransitions();
    }

    /**
     * 开始就诊（从等待状态转换到就诊中）
     *
     * @param recordId 医疗记录ID
     * @return 是否成功
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean startConsultation(Long recordId) {
        return transitionTo(recordId, MedicalRecordStatus.IN_PROGRESS);
    }

    /**
     * 开始取药（从已开方状态转换到取药中）
     *
     * @param recordId 医疗记录ID
     * @return 是否成功
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean startDispensing(Long recordId) {
        return transitionTo(recordId, MedicalRecordStatus.DISPENSING);
    }

    /**
     * 开始煎药（从取药中状态转换到煎药中）
     *
     * @param recordId 医疗记录ID
     * @return 是否成功
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean startDecocting(Long recordId) {
        return transitionTo(recordId, MedicalRecordStatus.DECOCTING);
    }

    /**
     * 完成整个流程（转换到已完成状态）
     *
     * @param recordId 医疗记录ID
     * @return 是否成功
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean finishProcess(Long recordId) {
        return transitionTo(recordId, MedicalRecordStatus.COMPLETED);
    }
}
