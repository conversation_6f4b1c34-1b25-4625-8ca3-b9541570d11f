package com.qili.clinic.domain.vo;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 药品小计计算响应对象
 *
 * <AUTHOR>
 * @date 2025-08-27
 */
@Data
public class DrugTreatmentSubtotalCalculateVo {

    /**
     * 药品ID
     */
    private Long id;

    /**
     * 药品名称
     */
    private String name;

    /**
     * 单价（分）
     */
    private Long price;

    /**
     * 数量
     */
    private BigDecimal amount;

    /**
     * 小计（分）
     */
    private Long subtotal;
}
