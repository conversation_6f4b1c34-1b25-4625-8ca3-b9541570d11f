package com.qili.clinic.patterns.state;

import cn.hutool.core.util.StrUtil;
import com.qili.clinic.domain.MedicalRecord;
import com.qili.clinic.domain.bo.MedicalRecordBo;
import com.qili.clinic.domain.bo.MedicalRecordLogBo;
import com.qili.clinic.enums.MedicalRecordStatus;
import com.qili.common.core.exception.ServiceException;
import lombok.extern.slf4j.Slf4j;

import java.util.Arrays;
import java.util.Date;

/**
 * 抽象医疗记录状态类
 * 提供状态的通用实现
 *
 * <AUTHOR>
 * @date 2025-08-28
 */
@Slf4j
public abstract class AbstractMedicalRecordState implements MedicalRecordState {

    @Override
    public boolean transitionTo(MedicalRecordContext context, MedicalRecordStatus targetStatus) {
        // 验证是否可以转换
        if (! canTransitionTo(targetStatus)) {
            log.error("状态转换失败: {} 不能转换到 {}", getStatus().getLabel(), targetStatus.getLabel());
            throw new ServiceException(String.format("当前状态 %s 不能转换到 %s",
                                                     getStatus().getLabel(), targetStatus.getLabel()));
        }

        // 执行状态转换前的业务逻辑
        if (! beforeTransition(context, targetStatus)) {
            log.error("状态转换前置检查失败: {} -> {}", getStatus().getLabel(), targetStatus.getLabel());
            return false;
        }

        // 更新医疗记录状态
        MedicalRecord record = context.getMedicalRecord();
        record.setStatus(targetStatus.getCode());
        record.setUpdateTime(new Date());

        // 保存到数据库
        boolean updateResult = context.getMedicalRecordMapper().updateById(record) > 0;
        if (! updateResult) {
            log.error("更新医疗记录状态失败: recordId={}, status={}", record.getId(), targetStatus.getCode());
            return false;
        }

        // 保存状态变更日志
        saveMedicalRecordLog(context, targetStatus);

        // 执行状态转换后的业务逻辑
        afterTransition(context, targetStatus);

        // 更新上下文状态
        MedicalRecordState newState = createStateInstance(targetStatus);
        context.setState(newState);

        log.info("状态转换成功: recordId={}, {} -> {}",
                 record.getId(), getStatus().getLabel(), targetStatus.getLabel());
        return true;
    }

    @Override
    public boolean cancel(MedicalRecordContext context) {
        // 默认实现：只有就诊中状态可以取消
        if (getStatus() != MedicalRecordStatus.IN_PROGRESS) {
            throw new ServiceException("当前状态不能取消");
        }

        return transitionTo(context, MedicalRecordStatus.CANCELLED);
    }

    @Override
    public boolean complete(MedicalRecordContext context, MedicalRecordBo bo) {
        // 默认实现：只有就诊中状态可以完成
        if (getStatus() != MedicalRecordStatus.IN_PROGRESS) {
            throw new ServiceException("当前状态不能完成");
        }

        // 执行完成前的业务逻辑（如保存支付记录）
        if (! beforeComplete(context, bo)) {
            return false;
        }

        return transitionTo(context, MedicalRecordStatus.PRESCRIPTION_DONE);
    }

    @Override
    public boolean canTransitionTo(MedicalRecordStatus targetStatus) {
        MedicalRecordStatus[] validTransitions = getValidTransitions();
        return Arrays.asList(validTransitions).contains(targetStatus);
    }

    /**
     * 状态转换前的业务逻辑
     *
     * @param context      上下文
     * @param targetStatus 目标状态
     * @return 是否通过前置检查
     */
    protected boolean beforeTransition(MedicalRecordContext context, MedicalRecordStatus targetStatus) {
        // 子类可以重写此方法实现特定的前置逻辑
        return true;
    }

    /**
     * 状态转换后的业务逻辑
     *
     * @param context      上下文
     * @param targetStatus 目标状态
     */
    protected void afterTransition(MedicalRecordContext context, MedicalRecordStatus targetStatus) {
        // 子类可以重写此方法实现特定的后置逻辑
    }

    /**
     * 完成前的业务逻辑
     *
     * @param context 上下文
     * @param bo      业务对象
     * @return 是否成功
     */
    protected boolean beforeComplete(MedicalRecordContext context, MedicalRecordBo bo) {
        // 子类可以重写此方法实现特定的完成前逻辑
        return true;
    }

    /**
     * 保存医疗记录日志
     *
     * @param context 上下文
     * @param status  状态
     */
    private void saveMedicalRecordLog(MedicalRecordContext context, MedicalRecordStatus status) {
        MedicalRecord record = context.getMedicalRecord();
        if (StrUtil.isBlank(status.getCode())) {
            return;
        }

        MedicalRecordLogBo logBo = new MedicalRecordLogBo();
        logBo.setAction(status.getCode());
        logBo.setActionName(status.getLabel());
        logBo.setActionTime(record.getUpdateTime());
        logBo.setRecordId(record.getId());

        context.getMedicalRecordLogService().insertAsync(logBo);
    }

    /**
     * 创建状态实例
     *
     * @param status 状态枚举
     * @return 状态实例
     */
    protected abstract MedicalRecordState createStateInstance(MedicalRecordStatus status);
}
