package com.qili.clinic.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.qili.clinic.domain.MedicalRecord;
import com.qili.clinic.domain.bo.MedicalRecordBo;
import com.qili.clinic.domain.bo.MedicalRecordLogBo;
import com.qili.clinic.domain.vo.MedicalRecordLogVo;
import com.qili.clinic.domain.vo.MedicalRecordVo;
import com.qili.clinic.enums.MedicalRecordStatus;
import com.qili.clinic.mapper.MedicalRecordMapper;
import com.qili.clinic.mapper.PatientMapper;
import com.qili.clinic.service.IMedicalRecordLogService;
import com.qili.clinic.service.IMedicalRecordService;
import com.qili.clinic.service.IPaymentService;
import com.qili.common.core.exception.ServiceException;
import com.qili.common.core.utils.MapstructUtils;
import com.qili.common.core.utils.StringUtils;
import com.qili.common.json.utils.JsonUtils;
import com.qili.common.mybatis.core.page.PageQuery;
import com.qili.common.mybatis.core.page.TableDataInfo;
import com.qili.common.redis.utils.RedisUtils;
import com.qili.common.satoken.utils.LoginHelper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RAtomicLong;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.Duration;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Map;

import static com.qili.common.tenant.helper.TenantHelper.getTenantId;

/**
 * 病历/就诊记录Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-08-06
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class MedicalRecordServiceImpl implements IMedicalRecordService {

    private final MedicalRecordMapper baseMapper;
    private final IMedicalRecordLogService medicalRecordLogService;
    private final IPaymentService paymentService;
    private final PatientMapper patientMapper;

    /**
     * 查询病历/就诊记录
     *
     * @param id 主键
     * @return 病历/就诊记录
     */
    @Override
    public MedicalRecordVo queryById(Long id) {
        MedicalRecordVo vo = baseMapper.selectVoById(id);
        vo.setStepList(medicalRecordLogService.queryListByRecordId(id));
        return vo;
    }

    /**
     * 分页查询病历/就诊记录列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 病历/就诊记录分页列表
     */
    @Override
    public TableDataInfo<MedicalRecordVo> queryPageList(MedicalRecordBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<MedicalRecord> lqw = buildQueryWrapper(bo);
        Page<MedicalRecordVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        result.getRecords().forEach(item -> {
            List<MedicalRecordLogVo> logVos = medicalRecordLogService.queryListByRecordId(item.getId());
            item.setStepList(logVos);
        });
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的病历/就诊记录列表
     *
     * @param bo 查询条件
     * @return 病历/就诊记录列表
     */
    @Override
    public List<MedicalRecordVo> queryList(MedicalRecordBo bo) {
        LambdaQueryWrapper<MedicalRecord> lqw = buildQueryWrapper(bo);
        List<MedicalRecordVo> medicalRecordVos = baseMapper.selectVoList(lqw);
        this.populateData(medicalRecordVos);
        return medicalRecordVos;
    }

    private LambdaQueryWrapper<MedicalRecord> buildQueryWrapper(MedicalRecordBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<MedicalRecord> lqw = Wrappers.lambdaQuery();
        lqw.orderByDesc(MedicalRecord::getId);
        lqw.eq(StringUtils.isNotBlank(bo.getVisitNo()), MedicalRecord::getVisitNo, bo.getVisitNo());
        lqw.eq(bo.getPatientId() != null, MedicalRecord::getPatientId, bo.getPatientId());
        lqw.like(StringUtils.isNotBlank(bo.getPatientName()), MedicalRecord::getPatientName, bo.getPatientName());
        lqw.eq(bo.getVisitTime() != null, MedicalRecord::getVisitTime, bo.getVisitTime());
        return lqw;
    }

    /**
     * 新增病历/就诊记录
     *
     * @param bo 病历/就诊记录
     * @return 是否新增成功
     */
    @Override
    public Long insertByBo(MedicalRecordBo bo) {
        MedicalRecord add = MapstructUtils.convert(bo, MedicalRecord.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        saveMedicalRecordLog(add);
        return add.getId();
    }

    /**
     * 修改病历/就诊记录
     *
     * @param bo 病历/就诊记录
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(MedicalRecordBo bo) {
        MedicalRecord update = MapstructUtils.convert(bo, MedicalRecord.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(MedicalRecord entity) {
        // 新增时校验
        if (ObjectUtil.isNull(entity.getId())) {
            entity.setDoctorId(LoginHelper.getUserId());
            entity.setDoctorName(LoginHelper.getLoginUser().getNickname());
            entity.setDeptId(LoginHelper.getDeptId());
            // 生成诊号
            entity.setVisitNo(generateVisitNo());
            entity.setVisitTime(new Date());
        }
    }

    /**
     * 校验并批量删除病历/就诊记录信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            // TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }

    @Override
    public MedicalRecordVo queryLatestByPatientId(Long patientId) {
        LambdaQueryWrapper<MedicalRecord> lqw = Wrappers.lambdaQuery();
        lqw.eq(MedicalRecord::getPatientId, patientId);
        lqw.orderByDesc(MedicalRecord::getId);
        lqw.last("limit 1");
        return baseMapper.selectVoOne(lqw);
    }

    @Override
    public void cancelById(Long id) {
        MedicalRecord record = baseMapper.selectById(id);
        if (record == null) {
            throw new ServiceException("就诊记录不存在");
        }
        // 状态不是就诊
        if (! MedicalRecordStatus.IN_PROGRESS.getCode().equals(record.getStatus())) {
            // 状态不是就诊中,不能取消
            throw new ServiceException("状态不是就诊中,不能取消");
        }
        record.setStatus(MedicalRecordStatus.CANCELLED.getCode());
        baseMapper.updateById(record);
        // 保存日志
        MedicalRecordBo bo = MapstructUtils.convert(record, MedicalRecordBo.class);
        if (ObjectUtil.isNull(bo)) {
            throw new ServiceException("日志转换失败");
        }
        saveMedicalRecordLog(record);
    }

    @Override
    @Transactional(rollbackFor = ServiceException.class)
    public void complete(MedicalRecordBo bo) {
        MedicalRecord record = baseMapper.selectById(bo.getId());
        if (record == null) {
            throw new ServiceException("就诊记录不存在");
        }
        // 状态不是就诊中
        if (! MedicalRecordStatus.IN_PROGRESS.getCode().equals(record.getStatus())) {
            throw new ServiceException("状态不是就诊中,不能完成");
        }
        BeanUtil.copyProperties(record, bo);
        // 保存支付记录
        paymentService.savePayment(bo);

        // 已开方状态
        record.setStatus(MedicalRecordStatus.PRESCRIPTION_DONE.getCode());
        // 将结构化数据转换为JSON存储
        serializationFieldJson(bo, record);
        baseMapper.updateById(record);
        // 保存日志
        saveMedicalRecordLog(record);
    }


    @Override
    public void savePrescription(MedicalRecordBo bo) {
        Long id = bo.getId();
        if (ObjectUtil.isNull(id)) {
            return;
        }
        MedicalRecord record = baseMapper.selectById(id);
        if (ObjectUtil.isNull(record)) {
            return;
        }
        if (! StrUtil.equals(record.getStatus(), MedicalRecordStatus.IN_PROGRESS.getCode())) {
            // 状态不是就诊中
            return;
        }
        // 将结构化数据转换为JSON存储
        serializationFieldJson(bo, record);
        baseMapper.updateById(record);
    }

    /**
     * 保存记录日志
     *
     * @param record 记录日志
     */
    private void saveMedicalRecordLog(MedicalRecord record) {
        String status = record.getStatus();
        if (StrUtil.isBlank(status)) {
            return;
        }
        MedicalRecordLogBo logBo = new MedicalRecordLogBo();
        String statusName = MedicalRecordStatus.getLabelByCode(status);
        logBo.setAction(status);
        logBo.setActionName(statusName);
        logBo.setActionTime(record.getUpdateTime());
        logBo.setRecordId(record.getId());
        medicalRecordLogService.insertAsync(logBo);
    }

    /**
     * 生成诊号
     *
     * @return 诊号
     */
    private String generateVisitNo() {
        String tenantId = getTenantId();
        if (StringUtils.isBlank(tenantId)) {
            throw new IllegalArgumentException("租户ID不能为空");
        }
        // 部门ID,用作一家公司分不同部门的情况
        Long deptId = LoginHelper.getDeptId();
        RedissonClient redissonClient = RedisUtils.getClient();
        String datePart = LocalDate.now().format(DateTimeFormatter.ofPattern("yyMMdd"));
        String key = String.format("visit_no:%s:%s:%s", tenantId, deptId, datePart);

        RAtomicLong counter = redissonClient.getAtomicLong(key);
        long seq = counter.incrementAndGet();

        if (seq == 1) {
            counter.expire(Duration.ofDays(1));
        }
        if (seq > 9999) {
            throw new IllegalStateException("当天诊号已达上限");
        }
        return datePart + String.format("%04d", seq);
    }

    private void populateData(Collection<MedicalRecordVo> vos) {
        List<Long> recordIds = vos.stream().map(MedicalRecordVo::getId).toList();
        Map<Long, List<MedicalRecordLogVo>> logVoMap = medicalRecordLogService.queryMapByRecordIds(recordIds);
        vos.forEach(vo -> vo.setStepList(logVoMap.getOrDefault(vo.getId(), List.of())));
    }

    /**
     * 将结构化数据转换为JSON存储
     *
     * @param bo     业务对象
     * @param record 记录
     */
    private void serializationFieldJson(MedicalRecordBo bo, MedicalRecord record) {
        if (bo.getDiagnosisData() != null) {
            record.setDiagnosisJson(JsonUtils.toJsonString(bo.getDiagnosisData()));
        }
        if (bo.getTcmPrescriptionData() != null) {
            record.setTcmPrescriptionJson(JsonUtils.toJsonString(bo.getTcmPrescriptionData()));
        }
        if (bo.getTcmpPrescriptionData() != null) {
            record.setTcmpPrescriptionJson(JsonUtils.toJsonString(bo.getTcmpPrescriptionData()));
        }
        if (bo.getWmPrescriptionData() != null) {
            record.setWmPrescriptionJson(JsonUtils.toJsonString(bo.getWmPrescriptionData()));
        }
        if (bo.getTreatmentProjectData() != null) {
            record.setTreatmentProjectJson(JsonUtils.toJsonString(bo.getTreatmentProjectData()));
        }
    }
}
