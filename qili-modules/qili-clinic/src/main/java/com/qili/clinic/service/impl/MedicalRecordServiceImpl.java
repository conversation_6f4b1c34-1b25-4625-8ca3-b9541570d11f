package com.qili.clinic.service.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.qili.clinic.domain.MedicalRecord;
import com.qili.clinic.domain.bo.MedicalRecordBo;
import com.qili.clinic.domain.bo.MedicalRecordLogBo;
import com.qili.clinic.domain.vo.MedicalRecordLogVo;
import com.qili.clinic.domain.vo.MedicalRecordVo;
import com.qili.clinic.enums.MedicalRecordStatus;
import com.qili.clinic.mapper.MedicalRecordMapper;
import com.qili.clinic.mapper.PatientMapper;
import com.qili.clinic.patterns.service.StateTransitionService;
import com.qili.clinic.service.IMedicalRecordLogService;
import com.qili.clinic.service.IMedicalRecordService;
import com.qili.clinic.service.IPaymentService;
import com.qili.common.core.exception.ServiceException;
import com.qili.common.core.utils.MapstructUtils;
import com.qili.common.core.utils.StringUtils;
import com.qili.common.json.utils.JsonUtils;
import com.qili.common.mybatis.core.page.PageQuery;
import com.qili.common.mybatis.core.page.TableDataInfo;
import com.qili.common.redis.utils.RedisUtils;
import com.qili.common.satoken.utils.LoginHelper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RAtomicLong;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.Duration;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Map;

import static com.qili.common.tenant.helper.TenantHelper.getTenantId;

/**
 * 病历/就诊记录Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-08-06
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class MedicalRecordServiceImpl implements IMedicalRecordService {

    private final MedicalRecordMapper baseMapper;
    private final IMedicalRecordLogService medicalRecordLogService;
    private final IPaymentService paymentService;
    private final PatientMapper patientMapper;
    private final StateTransitionService stateTransitionService;

    /**
     * 查询病历/就诊记录
     *
     * @param id 主键
     * @return 病历/就诊记录
     */
    @Override
    public MedicalRecordVo queryById(Long id) {
        MedicalRecordVo vo = baseMapper.selectVoById(id);
        vo.setStepList(medicalRecordLogService.queryListByRecordId(id));
        return vo;
    }

    /**
     * 分页查询病历/就诊记录列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 病历/就诊记录分页列表
     */
    @Override
    public TableDataInfo<MedicalRecordVo> queryPageList(MedicalRecordBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<MedicalRecord> lqw = buildQueryWrapper(bo);
        Page<MedicalRecordVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        result.getRecords().forEach(item -> {
            List<MedicalRecordLogVo> logVos = medicalRecordLogService.queryListByRecordId(item.getId());
            item.setStepList(logVos);
        });
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的病历/就诊记录列表
     *
     * @param bo 查询条件
     * @return 病历/就诊记录列表
     */
    @Override
    public List<MedicalRecordVo> queryList(MedicalRecordBo bo) {
        LambdaQueryWrapper<MedicalRecord> lqw = buildQueryWrapper(bo);
        List<MedicalRecordVo> medicalRecordVos = baseMapper.selectVoList(lqw);
        this.populateData(medicalRecordVos);
        return medicalRecordVos;
    }

    private LambdaQueryWrapper<MedicalRecord> buildQueryWrapper(MedicalRecordBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<MedicalRecord> lqw = Wrappers.lambdaQuery();
        lqw.orderByDesc(MedicalRecord::getId);
        lqw.eq(StringUtils.isNotBlank(bo.getVisitNo()), MedicalRecord::getVisitNo, bo.getVisitNo());
        lqw.eq(bo.getPatientId() != null, MedicalRecord::getPatientId, bo.getPatientId());
        lqw.like(StringUtils.isNotBlank(bo.getPatientName()), MedicalRecord::getPatientName, bo.getPatientName());
        lqw.eq(bo.getVisitTime() != null, MedicalRecord::getVisitTime, bo.getVisitTime());
        return lqw;
    }

    /**
     * 新增病历/就诊记录
     *
     * @param bo 病历/就诊记录
     * @return 是否新增成功
     */
    @Override
    public Long insertByBo(MedicalRecordBo bo) {
        MedicalRecord add = MapstructUtils.convert(bo, MedicalRecord.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        saveMedicalRecordLog(add);
        return add.getId();
    }

    /**
     * 修改病历/就诊记录
     *
     * @param bo 病历/就诊记录
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(MedicalRecordBo bo) {
        MedicalRecord update = MapstructUtils.convert(bo, MedicalRecord.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(MedicalRecord entity) {
        // 新增时校验
        if (ObjectUtil.isNull(entity.getId())) {
            entity.setDoctorId(LoginHelper.getUserId());
            entity.setDoctorName(LoginHelper.getLoginUser().getNickname());
            entity.setDeptId(LoginHelper.getDeptId());
            // 生成诊号
            entity.setVisitNo(generateVisitNo());
            entity.setVisitTime(new Date());
        }
    }

    /**
     * 校验并批量删除病历/就诊记录信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            // TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }

    @Override
    public MedicalRecordVo queryLatestByPatientId(Long patientId) {
        LambdaQueryWrapper<MedicalRecord> lqw = Wrappers.lambdaQuery();
        lqw.eq(MedicalRecord::getPatientId, patientId);
        lqw.orderByDesc(MedicalRecord::getId);
        lqw.last("limit 1");
        return baseMapper.selectVoOne(lqw);
    }

    @Override
    public void cancelById(Long id) {
        // 使用状态模式处理取消逻辑
        boolean success = stateTransitionService.cancel(id);
        if (! success) {
            throw new ServiceException("取消就诊失败");
        }
    }

    @Override
    @Transactional(rollbackFor = ServiceException.class)
    public void complete(MedicalRecordBo bo) {
        // 使用状态模式处理完成逻辑
        boolean success = stateTransitionService.complete(bo.getId(), bo);
        if (! success) {
            throw new ServiceException("完成就诊失败");
        }
    }


    @Override
    public void savePrescription(MedicalRecordBo bo) {
        Long id = bo.getId();
        if (ObjectUtil.isNull(id)) {
            return;
        }
        MedicalRecord record = baseMapper.selectById(id);
        if (ObjectUtil.isNull(record)) {
            return;
        }
        if (! StrUtil.equals(record.getStatus(), MedicalRecordStatus.IN_PROGRESS.getCode())) {
            // 状态不是就诊中
            return;
        }
        // 将结构化数据转换为JSON存储
        serializationFieldJson(bo, record);
        baseMapper.updateById(record);
    }

    /**
     * 开始就诊（从等待状态转换到就诊中）
     *
     * @param recordId 医疗记录ID
     * @return 是否成功
     */
    public boolean startConsultation(Long recordId) {
        return stateTransitionService.startConsultation(recordId);
    }

    /**
     * 开始取药（从已开方状态转换到取药中）
     *
     * @param recordId 医疗记录ID
     * @return 是否成功
     */
    public boolean startDispensing(Long recordId) {
        return stateTransitionService.startDispensing(recordId);
    }

    /**
     * 开始煎药（从取药中状态转换到煎药中）
     *
     * @param recordId 医疗记录ID
     * @return 是否成功
     */
    public boolean startDecocting(Long recordId) {
        return stateTransitionService.startDecocting(recordId);
    }

    /**
     * 完成整个流程（转换到已完成状态）
     *
     * @param recordId 医疗记录ID
     * @return 是否成功
     */
    public boolean finishProcess(Long recordId) {
        return stateTransitionService.finishProcess(recordId);
    }

    /**
     * 检查是否可以转换到目标状态
     *
     * @param recordId     医疗记录ID
     * @param targetStatus 目标状态
     * @return 是否可以转换
     */
    public boolean canTransitionTo(Long recordId, MedicalRecordStatus targetStatus) {
        return stateTransitionService.canTransitionTo(recordId, targetStatus);
    }

    /**
     * 通用状态转换方法
     *
     * @param recordId     医疗记录ID
     * @param targetStatus 目标状态
     * @param data         额外数据
     * @param action       操作类型
     * @return 是否成功
     */
    @Override
    public boolean transitionState(Long recordId, MedicalRecordStatus targetStatus, Object data, String action) {
        try {
            boolean success;
            // 根据目标状态和操作类型选择合适的处理方式
            switch (targetStatus) {
                case CANCELLED:
                    success = stateTransitionService.cancel(recordId);
                    break;
                case PRESCRIPTION_DONE:
                    // 如果有额外数据，说明是完成就诊操作
                    if (data != null) {
                        // 将data转换为MedicalRecordBo
                        MedicalRecordBo bo = convertToMedicalRecordBo(data, recordId);
                        success = stateTransitionService.complete(recordId, bo);
                    } else {
                        success = stateTransitionService.transitionTo(recordId, targetStatus);
                    }
                    break;
                default:
                    // 普通状态转换
                    success = stateTransitionService.transitionTo(recordId, targetStatus);
                    break;
            }
            return success;
        } catch (Exception e) {
            log.error("状态转换失败: recordId={}, targetStatus={}, error={}", recordId, targetStatus, e.getMessage());
            throw new ServiceException("状态转换失败: " + e.getMessage());
        }
    }

    /**
     * 将请求数据转换为MedicalRecordBo
     */
    private MedicalRecordBo convertToMedicalRecordBo(Object data, Long recordId) {
        // 这里可以使用Jackson或其他工具进行转换
        // 简单实现，实际项目中可能需要更复杂的转换逻辑
        if (data instanceof MedicalRecordBo bo) {
            bo.setId(recordId);
            return bo;
        }
        // 如果data是Map或JSON，可以转换为MedicalRecordBo
        // 这里简化处理
        MedicalRecordBo bo = new MedicalRecordBo();
        bo.setId(recordId);
        return bo;
    }

    /**
     * 获取可转换的状态列表
     *
     * @param recordId 医疗记录ID
     * @return 可转换的状态数组
     */
    @Override
    public MedicalRecordStatus[] getValidTransitions(Long recordId) {
        return stateTransitionService.getValidTransitions(recordId);
    }

    /**
     * 保存记录日志
     *
     * @param record 记录日志
     */
    private void saveMedicalRecordLog(MedicalRecord record) {
        String status = record.getStatus();
        if (StrUtil.isBlank(status)) {
            return;
        }
        MedicalRecordLogBo logBo = new MedicalRecordLogBo();
        String statusName = MedicalRecordStatus.getLabelByCode(status);
        logBo.setAction(status);
        logBo.setActionName(statusName);
        logBo.setActionTime(record.getUpdateTime());
        logBo.setRecordId(record.getId());
        medicalRecordLogService.insertAsync(logBo);
    }

    /**
     * 生成诊号
     *
     * @return 诊号
     */
    private String generateVisitNo() {
        String tenantId = getTenantId();
        if (StringUtils.isBlank(tenantId)) {
            throw new IllegalArgumentException("租户ID不能为空");
        }
        // 部门ID,用作一家公司分不同部门的情况
        Long deptId = LoginHelper.getDeptId();
        RedissonClient redissonClient = RedisUtils.getClient();
        String datePart = LocalDate.now().format(DateTimeFormatter.ofPattern("yyMMdd"));
        String key = String.format("visit_no:%s:%s:%s", tenantId, deptId, datePart);

        RAtomicLong counter = redissonClient.getAtomicLong(key);
        long seq = counter.incrementAndGet();

        if (seq == 1) {
            counter.expire(Duration.ofDays(1));
        }
        if (seq > 9999) {
            throw new IllegalStateException("当天诊号已达上限");
        }
        return datePart + String.format("%04d", seq);
    }

    private void populateData(Collection<MedicalRecordVo> vos) {
        List<Long> recordIds = vos.stream().map(MedicalRecordVo::getId).toList();
        Map<Long, List<MedicalRecordLogVo>> logVoMap = medicalRecordLogService.queryMapByRecordIds(recordIds);
        vos.forEach(vo -> vo.setStepList(logVoMap.getOrDefault(vo.getId(), List.of())));
    }

    /**
     * 将结构化数据转换为JSON存储
     *
     * @param bo     业务对象
     * @param record 记录
     */
    private void serializationFieldJson(MedicalRecordBo bo, MedicalRecord record) {
        if (bo.getDiagnosisData() != null) {
            record.setDiagnosisJson(JsonUtils.toJsonString(bo.getDiagnosisData()));
        }
        if (bo.getTcmPrescriptionData() != null) {
            record.setTcmPrescriptionJson(JsonUtils.toJsonString(bo.getTcmPrescriptionData()));
        }
        if (bo.getTcmpPrescriptionData() != null) {
            record.setTcmpPrescriptionJson(JsonUtils.toJsonString(bo.getTcmpPrescriptionData()));
        }
        if (bo.getWmPrescriptionData() != null) {
            record.setWmPrescriptionJson(JsonUtils.toJsonString(bo.getWmPrescriptionData()));
        }
        if (bo.getTreatmentProjectData() != null) {
            record.setTreatmentProjectJson(JsonUtils.toJsonString(bo.getTreatmentProjectData()));
        }
    }
}
