package com.qili.clinic.domain.dto;

import com.qili.clinic.enums.MedicalRecordStatus;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 状态转换请求DTO
 *
 * <AUTHOR>
 * @date 2025-08-28
 */
@Data
public class StateTransitionRequest {

    /**
     * 医疗记录ID
     */
    @NotNull(message = "医疗记录ID不能为空")
    private Long recordId;

    /**
     * 目标状态
     */
    @NotNull(message = "目标状态不能为空")
    private MedicalRecordStatus targetStatus;

    /**
     * 额外数据（用于complete操作时传递处方数据等）
     */
    private Object data;

    /**
     * 操作类型（可选，用于区分不同的业务操作）
     */
    private String action;

    /**
     * 备注信息
     */
    private String remark;
}
