package com.qili.clinic.patterns.state.impl;

import cn.hutool.core.bean.BeanUtil;
import com.qili.clinic.domain.bo.MedicalRecordBo;
import com.qili.clinic.enums.MedicalRecordStatus;
import com.qili.clinic.patterns.state.AbstractMedicalRecordState;
import com.qili.clinic.patterns.state.MedicalRecordContext;
import com.qili.clinic.patterns.state.MedicalRecordState;
import lombok.extern.slf4j.Slf4j;

/**
 * 就诊中状态
 *
 * <AUTHOR>
 * @date 2025-08-28
 */
@Slf4j
public class InProgressState extends AbstractMedicalRecordState {

    @Override
    public MedicalRecordStatus getStatus() {
        return MedicalRecordStatus.IN_PROGRESS;
    }

    @Override
    public MedicalRecordStatus[] getValidTransitions() {
        return new MedicalRecordStatus[]{
            MedicalRecordStatus.PRESCRIPTION_DONE,  // 完成开方
            MedicalRecordStatus.CANCELLED           // 取消就诊
        };
    }

    @Override
    protected boolean beforeComplete(MedicalRecordContext context, MedicalRecordBo bo) {
        try {
            // 复制记录数据
            BeanUtil.copyProperties(context.getMedicalRecord(), bo);

            // 保存支付记录
            context.getPaymentService().savePayment(bo);

            // 序列化字段为JSON（这里需要实现序列化逻辑）
            serializationFieldJson(bo, context.getMedicalRecord());

            return true;
        } catch (Exception e) {
            log.error("完成就诊前置处理失败", e);
            return false;
        }
    }

    @Override
    protected MedicalRecordState createStateInstance(MedicalRecordStatus status) {
        return StateFactory.createState(status);
    }

    /**
     * 将结构化数据转换为JSON存储
     *
     * @param bo     业务对象
     * @param record 医疗记录
     */
    private void serializationFieldJson(MedicalRecordBo bo, com.qili.clinic.domain.MedicalRecord record) {
        if (bo.getDiagnosisData() != null) {
            record.setDiagnosisJson(com.qili.common.json.utils.JsonUtils.toJsonString(bo.getDiagnosisData()));
        }
        if (bo.getTcmPrescriptionData() != null) {
            record.setTcmPrescriptionJson(com.qili.common.json.utils.JsonUtils.toJsonString(bo.getTcmPrescriptionData()));
        }
        if (bo.getTcmpPrescriptionData() != null) {
            record.setTcmpPrescriptionJson(com.qili.common.json.utils.JsonUtils.toJsonString(bo.getTcmpPrescriptionData()));
        }
        if (bo.getWmPrescriptionData() != null) {
            record.setWmPrescriptionJson(com.qili.common.json.utils.JsonUtils.toJsonString(bo.getWmPrescriptionData()));
        }
        if (bo.getTreatmentProjectData() != null) {
            record.setTreatmentProjectJson(com.qili.common.json.utils.JsonUtils.toJsonString(bo.getTreatmentProjectData()));
        }
    }
}
