package com.qili.clinic.service;

import com.qili.clinic.domain.bo.DrugTreatmentSubtotalCalculateBo;
import com.qili.clinic.domain.bo.TreatmentProjectBo;
import com.qili.clinic.domain.vo.DrugTreatmentSubtotalCalculateVo;
import com.qili.clinic.domain.vo.TreatmentProjectVo;
import com.qili.common.mybatis.core.page.PageQuery;
import com.qili.common.mybatis.core.page.TableDataInfo;

import java.util.Collection;
import java.util.List;

/**
 * 诊疗项目Service接口
 *
 * <AUTHOR>
 * @date 2025-08-08
 */
public interface ITreatmentProjectService {

    /**
     * 查询诊疗项目
     *
     * @param id 主键
     * @return 诊疗项目
     */
    TreatmentProjectVo queryById(Long id);

    /**
     * 分页查询诊疗项目列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 诊疗项目分页列表
     */
    TableDataInfo<TreatmentProjectVo> queryPageList(TreatmentProjectBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的诊疗项目列表
     *
     * @param bo 查询条件
     * @return 诊疗项目列表
     */
    List<TreatmentProjectVo> queryList(TreatmentProjectBo bo);

    /**
     * 新增诊疗项目
     *
     * @param bo 诊疗项目
     * @return 是否新增成功
     */
    Boolean insertByBo(TreatmentProjectBo bo);

    /**
     * 修改诊疗项目
     *
     * @param bo 诊疗项目
     * @return 是否修改成功
     */
    Boolean updateByBo(TreatmentProjectBo bo);

    /**
     * 校验并批量删除诊疗项目信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 根据ID集合批量查询诊疗项目信息
     *
     * @param ids ID集合
     * @return 诊疗项目信息列表
     */
    List<TreatmentProjectVo> queryByIds(Collection<Long> ids);

    /**
     * 计算诊疗项目小计价格
     *
     * @param bo bo
     * @return 计算结果
     */
    DrugTreatmentSubtotalCalculateVo calculateSubtotal(DrugTreatmentSubtotalCalculateBo bo);
}
