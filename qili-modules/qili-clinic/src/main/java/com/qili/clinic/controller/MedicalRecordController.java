package com.qili.clinic.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.qili.clinic.domain.bo.MedicalRecordBo;
import com.qili.clinic.domain.vo.MedicalRecordVo;
import com.qili.clinic.service.IMedicalRecordService;
import com.qili.common.core.domain.R;
import com.qili.common.core.validate.AddGroup;
import com.qili.common.core.validate.EditGroup;
import com.qili.common.excel.utils.ExcelUtil;
import com.qili.common.idempotent.annotation.RepeatSubmit;
import com.qili.common.log.annotation.Log;
import com.qili.common.log.enums.BusinessType;
import com.qili.common.mybatis.core.page.PageQuery;
import com.qili.common.mybatis.core.page.TableDataInfo;
import com.qili.common.web.core.BaseController;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 病历/就诊记录
 *
 * <AUTHOR>
 * @date 2025-08-06
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/clinic/medicalRecord")
public class MedicalRecordController extends BaseController {

    private final IMedicalRecordService medicalRecordService;

    /**
     * 查询病历/就诊记录列表
     */
    @SaCheckPermission("clinic:medicalRecord:list")
    @GetMapping("/list")
    public TableDataInfo<MedicalRecordVo> list(MedicalRecordBo bo, PageQuery pageQuery) {
        return medicalRecordService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出病历/就诊记录列表
     */
    @SaCheckPermission("clinic:medicalRecord:export")
    @Log(title = "病历/就诊记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(MedicalRecordBo bo, HttpServletResponse response) {
        List<MedicalRecordVo> list = medicalRecordService.queryList(bo);
        ExcelUtil.exportExcel(list, "病历/就诊记录", MedicalRecordVo.class, response);
    }

    /**
     * 获取病历/就诊记录详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("clinic:medicalRecord:query")
    @GetMapping("/{id}")
    public R<MedicalRecordVo> getInfo(@NotNull(message = "主键不能为空") @PathVariable Long id) {
        return R.ok(medicalRecordService.queryById(id));
    }

    /**
     * 新增病历/就诊记录
     */
    @SaCheckPermission("clinic:medicalRecord:add")
    @Log(title = "病历/就诊记录", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Long> add(@Validated(AddGroup.class) @RequestBody MedicalRecordBo bo) {
        return R.ok(medicalRecordService.insertByBo(bo));
    }

    /**
     * 修改病历/就诊记录
     */
    @SaCheckPermission("clinic:medicalRecord:edit")
    @Log(title = "病历/就诊记录", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody MedicalRecordBo bo) {
        return toAjax(medicalRecordService.updateByBo(bo));
    }

    /**
     * 删除病历/就诊记录
     *
     * @param ids 主键串
     */
    @SaCheckPermission("clinic:medicalRecord:remove")
    @Log(title = "病历/就诊记录", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空") @PathVariable Long[] ids) {
        return toAjax(medicalRecordService.deleteWithValidByIds(List.of(ids), true));
    }

    /**
     * 取消就诊
     *
     * @param id 主键
     * @return 结果
     */
    @SaCheckPermission("clinic:medicalRecord:edit")
    @Log(title = "病历/就诊记录", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping("/cancel/{id}")
    public R<Void> cancel(@PathVariable Long id) {
        medicalRecordService.cancelById(id);
        return R.ok("取消成功");
    }

    /**
     * 完成就诊
     *
     * @param id 主键
     * @return 结果
     */
    @SaCheckPermission("clinic:medicalRecord:edit")
    @Log(title = "病历/就诊记录", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping("/complete")
    public R<Void> complete(@RequestBody MedicalRecordBo bo) {
        medicalRecordService.complete(bo);
        return R.ok("已完成接诊");
    }

    /**
     * 保存处方,用于定时保存处方
     *
     * @param bo 病历/就诊记录
     * @return 结果
     */
    @SaCheckPermission("clinic:medicalRecord:edit")
    @Log(title = "病历/就诊记录", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping("/prescription")
    public R<Void> savePrescription(@RequestBody MedicalRecordBo bo) {
        medicalRecordService.savePrescription(bo);
        return R.ok("保存处方成功");
    }

    // ==================== 状态模式接口 ====================

    /**
     * 开始就诊（等待 -> 就诊中）
     *
     * @param id 医疗记录ID
     * @return 结果
     */
    @SaCheckPermission("clinic:medicalRecord:edit")
    @Log(title = "开始就诊", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping("/start/{id}")
    public R<Void> startConsultation(@PathVariable Long id) {
        boolean success = medicalRecordService.startConsultation(id);
        return success ? R.ok("开始就诊成功") : R.fail("开始就诊失败");
    }

    /**
     * 开始取药（已开方 -> 取药中）
     *
     * @param id 医疗记录ID
     * @return 结果
     */
    @SaCheckPermission("clinic:medicalRecord:edit")
    @Log(title = "开始取药", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping("/dispensing/{id}")
    public R<Void> startDispensing(@PathVariable Long id) {
        boolean success = medicalRecordService.startDispensing(id);
        return success ? R.ok("开始取药成功") : R.fail("开始取药失败");
    }

    /**
     * 开始煎药（取药中 -> 煎药中）
     *
     * @param id 医疗记录ID
     * @return 结果
     */
    @SaCheckPermission("clinic:medicalRecord:edit")
    @Log(title = "开始煎药", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping("/decocting/{id}")
    public R<Void> startDecocting(@PathVariable Long id) {
        boolean success = medicalRecordService.startDecocting(id);
        return success ? R.ok("开始煎药成功") : R.fail("开始煎药失败");
    }

    /**
     * 完成流程（-> 已完成）
     *
     * @param id 医疗记录ID
     * @return 结果
     */
    @SaCheckPermission("clinic:medicalRecord:edit")
    @Log(title = "完成流程", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping("/finish/{id}")
    public R<Void> finishProcess(@PathVariable Long id) {
        boolean success = medicalRecordService.finishProcess(id);
        return success ? R.ok("流程完成") : R.fail("完成流程失败");
    }
}
