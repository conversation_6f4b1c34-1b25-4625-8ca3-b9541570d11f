package com.qili.clinic.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.NumberUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.qili.clinic.domain.Drug;
import com.qili.clinic.domain.bo.DrugBo;
import com.qili.clinic.domain.vo.DrugTreatmentSubtotalCalculateVo;
import com.qili.clinic.domain.vo.DrugVo;
import com.qili.clinic.mapper.DrugMapper;
import com.qili.clinic.service.IDrugService;
import com.qili.common.core.exception.ServiceException;
import com.qili.common.core.utils.MapstructUtils;
import com.qili.common.core.utils.StringUtils;
import com.qili.common.mybatis.core.page.PageQuery;
import com.qili.common.mybatis.core.page.TableDataInfo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;

/**
 * 药品信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-23
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class DrugServiceImpl implements IDrugService {

    private final DrugMapper baseMapper;

    /**
     * 查询药品信息
     *
     * @param id 主键
     * @return 药品信息
     */
    @Override
    public DrugVo queryById(Long id) {
        DrugVo drugVo = baseMapper.selectVoById(id);
        drugVo.setPriceYuan(formatPriceYuan(drugVo.getPrice()));
        return drugVo;
    }

    /**
     * 分页查询药品信息列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 药品信息分页列表
     */
    @Override
    public TableDataInfo<DrugVo> queryPageList(DrugBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<Drug> lqw = buildQueryWrapper(bo);
        Page<DrugVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        result.getRecords().forEach(drugVo -> drugVo.setPriceYuan(formatPriceYuan(drugVo.getPrice())));
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的药品信息列表
     *
     * @param bo 查询条件
     * @return 药品信息列表
     */
    @Override
    public List<DrugVo> queryList(DrugBo bo) {
        LambdaQueryWrapper<Drug> lqw = buildQueryWrapper(bo);
        List<DrugVo> drugVos = baseMapper.selectVoList(lqw);
        drugVos.forEach(drugVo -> drugVo.setPriceYuan(formatPriceYuan(drugVo.getPrice())));
        return drugVos;
    }

    private LambdaQueryWrapper<Drug> buildQueryWrapper(DrugBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<Drug> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(Drug::getId);
        String searchValue = bo.getSearchValue();
        if (StringUtils.isNotBlank(searchValue)) {
            lqw.and(wrapper -> wrapper
                .like(Drug::getName, searchValue)
                .or()
                .like(Drug::getAlias, searchValue.toUpperCase(Locale.ROOT)));
        }
        lqw.like(StringUtils.isNotBlank(bo.getName()), Drug::getName, bo.getName());
        lqw.eq(StringUtils.isNotBlank(bo.getCategory()), Drug::getCategory, bo.getCategory());
        return lqw;
    }

    /**
     * 新增药品信息
     *
     * @param bo 药品信息
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(DrugBo bo) {
        Drug add = MapstructUtils.convert(bo, Drug.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改药品信息
     *
     * @param bo 药品信息
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(DrugBo bo) {
        Drug update = MapstructUtils.convert(bo, Drug.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(Drug entity) {
        // TODO 做一些数据校验,如唯一约束
        entity.setPrice(entity.getPrice() * 100);
    }

    /**
     * 校验并批量删除药品信息信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            // TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }

    @Override
    public List<DrugVo> queryByIds(Collection<Long> ids) {
        if (CollUtil.isEmpty(ids)) {
            return new ArrayList<>();
        }
        List<DrugVo> drugVos = baseMapper.selectVoByIds(ids);
        drugVos.forEach(drugVo -> drugVo.setPriceYuan(formatPriceYuan(drugVo.getPrice())));
        return drugVos;
    }

    @Override
    public DrugTreatmentSubtotalCalculateVo calculateSubtotal(Long id, BigDecimal amount) {
        // 查询药品信息
        DrugVo drug = queryById(id);
        if (drug == null) {
            throw new ServiceException("药品不存在，ID: " + id);
        }
        // 计算小计（分）
        // 单价（分） * 数量 = 小计（分）
        BigDecimal unitPriceBigDecimal = new BigDecimal(drug.getPrice());
        BigDecimal subtotalBigDecimal = unitPriceBigDecimal.multiply(amount);
        Long subtotal = subtotalBigDecimal.longValue();

        // 构建响应对象
        DrugTreatmentSubtotalCalculateVo response = new DrugTreatmentSubtotalCalculateVo();
        response.setId(id);
        response.setName(drug.getName());
        response.setPrice(drug.getPrice());
        response.setAmount(amount);
        response.setSubtotal(subtotal);
        return response;
    }

    /**
     * 格式化价格为元
     *
     * @param price 价格，单位分
     * @return 格式化后的价格，单位元
     */
    private String formatPriceYuan(Long price) {
        if (price != null) {
            return NumberUtil.div(price.toString(), "100", 2, RoundingMode.HALF_UP).toString();
        }
        return "0.00";
    }
}
