package com.qili.clinic.patterns.state.impl;

import com.qili.clinic.enums.MedicalRecordStatus;
import com.qili.clinic.patterns.state.AbstractMedicalRecordState;
import com.qili.clinic.patterns.state.MedicalRecordState;
import lombok.extern.slf4j.Slf4j;

/**
 * 已开方状态
 *
 * <AUTHOR>
 * @date 2025-08-28
 */
@Slf4j
public class PrescriptionDoneState extends AbstractMedicalRecordState {

    @Override
    public MedicalRecordStatus getStatus() {
        return MedicalRecordStatus.PRESCRIPTION_DONE;
    }

    @Override
    public MedicalRecordStatus[] getValidTransitions() {
        return new MedicalRecordStatus[]{
            MedicalRecordStatus.DISPENSING,  // 开始取药
            MedicalRecordStatus.COMPLETED    // 直接完成（无需取药）
        };
    }

    @Override
    protected MedicalRecordState createStateInstance(MedicalRecordStatus status) {
        return StateFactory.createState(status);
    }
}
