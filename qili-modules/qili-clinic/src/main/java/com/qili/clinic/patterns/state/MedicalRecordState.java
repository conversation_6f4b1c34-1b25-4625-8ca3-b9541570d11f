package com.qili.clinic.patterns.state;

import com.qili.clinic.domain.MedicalRecord;
import com.qili.clinic.domain.bo.MedicalRecordBo;
import com.qili.clinic.enums.MedicalRecordStatus;

/**
 * 医疗记录状态接口
 * 定义状态的基本行为
 *
 * <AUTHOR>
 * @date 2025-08-28
 */
public interface MedicalRecordState {

    /**
     * 获取当前状态
     *
     * @return 状态枚举
     */
    MedicalRecordStatus getStatus();

    /**
     * 转换到下一个状态
     *
     * @param context 状态上下文
     * @param targetStatus 目标状态
     * @return 是否转换成功
     */
    boolean transitionTo(MedicalRecordContext context, MedicalRecordStatus targetStatus);

    /**
     * 取消就诊
     *
     * @param context 状态上下文
     * @return 是否取消成功
     */
    boolean cancel(MedicalRecordContext context);

    /**
     * 完成就诊（开方）
     *
     * @param context 状态上下文
     * @param bo 医疗记录业务对象
     * @return 是否完成成功
     */
    boolean complete(MedicalRecordContext context, MedicalRecordBo bo);

    /**
     * 验证是否可以转换到目标状态
     *
     * @param targetStatus 目标状态
     * @return 是否可以转换
     */
    boolean canTransitionTo(MedicalRecordStatus targetStatus);

    /**
     * 获取可转换的状态列表
     *
     * @return 可转换的状态数组
     */
    MedicalRecordStatus[] getValidTransitions();
}
